import { useState, useMemo, useEffect, useCallback } from 'react';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { Button } from '@tigergraph/app-ui-lib/button';
import { Drawer, DrawerBody, DrawerAction, DrawerHeader } from '@/components/Drawer';
import { useWorkspaceContext } from '@/contexts/workspaceContext';
import useEditorTheme from '@/pages/editor/useEditorTheme';
import useCopyClipboard from 'react-use-clipboard';
import { showToast, StyledToast } from '@/components/styledToasterContainer';
import { GsqlQueryMeta, QueryMetaLogic, QueryParam, QueryParamType } from '@tigergraph/tools-models';
import { MdPlayArrow } from 'react-icons/md';
import { QueryCommand } from '@/pages/editor/result/CommandExecutor';
import { useEditorContext } from '@/contexts/graphEditorContext';
import { KIND } from 'baseui/toast';
import QueryParamForm from './params/QueryParamForm';
import QueryConfig from '@/pages/editor/header/RunConfig';
import { getQueryDefalutPayloadByParams, ParamError, ParamErrors } from '@/utils/queryParam';
import { useSchema } from '@/utils/useSchema';
import { CreateToken } from './CreateToken';
import { Input } from '@tigergraph/app-ui-lib/input';
import { CopyIcon } from '@/pages/home/<USER>';
import TooltipLabel from '@/components/TooltipLabel';
import SnippetsCopier from '@/pages/editor/query/SnippetsCopier';
import HttpMethodBadge from '@/components/HttpMethodBadge';
import { ApiType } from '@/pages/workgroup/tab/restPP/type';

export interface RunQueryDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onRun: (cmd: QueryCommand) => void;
  query: GsqlQueryMeta;
  graphName: string;
}

export default function RunQueryDrawer(props: RunQueryDrawerProps) {
  const { isOpen, onClose, onRun, query } = props;
  const [css, theme] = useStyletron();
  const editorTheme = useEditorTheme({ background: theme.colors['input.background'] });
  const { currentWorkspace } = useWorkspaceContext();
  const { data: schema } = useSchema(currentWorkspace, props.graphName, isOpen);

  // We need to extract the graph name from the endpoint structure
  const graphName = props.graphName;
  const queryURL = `https://${currentWorkspace?.nginx_host}/restpp/query/${graphName}/${query.name}`;

  const [urlCopied, setUrlCopied] = useCopyClipboard(queryURL, {
    successDuration: 1000,
  });
  useEffect(() => {
    if (urlCopied) {
      showToast({
        kind: 'positive',
        message: 'URL copied to clipboard successfully.',
      });
    }
  }, [urlCopied]);

  const queryParams: QueryParam[] = useMemo(() => {
    const params = query.endpoint?.query?.[graphName]?.[query.name]?.['GET/POST']?.parameters || {};
    return QueryMetaLogic.convertGSQLParameters(params);
  }, [query, graphName]);

  const [queryPayload, setQueryPayload] = useState<Record<any, any>>({});
  const [paramErrors, setParamErrors] = useState<ParamErrors>({});
  useEffect(() => {
    const defaultPayload = getQueryDefalutPayloadByParams(queryParams);
    setQueryPayload(defaultPayload);
  }, [graphName, query.name, queryParams]);

  const { memoryLimit, timeLimit, profile } = useEditorContext();

  const validateSimpleTypeInput = (paramType: QueryParamType, value: any): string => {
    if (paramType.type === 'INT') {
      if (!/^-?\d+$/.test(value)) {
        return 'Please input a valid INT.';
      }
    }

    if (paramType.type === 'UINT') {
      if (!/^\d+$/.test(value)) {
        return 'Please input a valid UINT.';
      }
    }

    if (paramType.type === 'FLOAT' || paramType.type === 'DOUBLE') {
      if (isNaN(Number(value)) || value === '') {
        return `Please input a valid ${paramType.type}.`;
      }
    }

    if (paramType.type === 'VERTEX') {
      if (!value?.id) {
        return 'Please input a valid vertex ID.';
      }
    }

    if (paramType.type === 'DATETIME') {
      if ((isNaN(Number(value)) && isNaN(Date.parse(value))) || value === '') {
        return 'Please input a valid DATETIME.';
      }
    }

    return '';
  };

  const validateInput = useCallback((param: QueryParam, value: any): { error: ParamError; hasError: boolean } => {
    const type = param.paramType.type;
    let hasError = false;
    let error: ParamError = '';
    if (type === 'LIST') {
      error = value.map((item: any) => validateSimpleTypeInput(param.paramType.elementType, item));
      hasError = (error as string[]).some((err: string) => !!err);
    } else if (type === 'MAP') {
      error = value.map(({ key, value }: { key: any; value: any }) => ({
        key: validateSimpleTypeInput(param.paramType.keyType, key),
        value: validateSimpleTypeInput(param.paramType.valueType, value),
      }));
      hasError = (error as { key: string; value: string }[]).some(({ key, value }) => !!key || !!value);
    } else {
      error = validateSimpleTypeInput(param.paramType, value);
      hasError = !!error;
    }

    return { error, hasError };
  }, []);

  const validateAllInputs = useCallback(
    (queryPayload: Record<string, any>) => {
      const errors: ParamErrors = {};
      let hasError = false;
      for (const param of queryParams) {
        const value = queryPayload[param.paramName];
        const type = param.paramType.type;
        const { error, hasError: _hasError } = validateInput(param, value);
        errors[param.paramName] = error;
        hasError = hasError || _hasError;
      }
      setParamErrors(errors);

      return hasError;
    },
    [queryParams, validateInput]
  );

  const onQueryParamChange = useCallback(
    (param: QueryParam, value: any) => {
      setQueryPayload((prev) => ({ ...prev, [param.paramName]: value }));

      const { error } = validateInput(param, value);
      setParamErrors((prev) => ({ ...prev, [param.paramName]: error }));
    },
    [validateInput]
  );

  const onRunClick = () => {
    if (validateAllInputs(queryPayload)) return;

    onRun(
      new QueryCommand(
        currentWorkspace!.workspace_id,
        graphName,
        query,
        queryPayload,
        {
          memoryLimit,
          timeLimit,
          profile,
        },
        {
          nginx_host: currentWorkspace!.nginx_host,
          tg_version: currentWorkspace!.tg_version,
        }
      )
    );
    onClose();
  };

  const [isCreateSecretOpen, setIsCreateSecretOpen] = useState(false);
  const [token, setToken] = useState('');

  return (
    <Drawer isOpen={isOpen} onClose={onClose} size="auto">
      <DrawerHeader>
        <div className={css({ display: 'flex', alignItems: 'center' })}>
          <MdPlayArrow size={24} /> <span className={css({ marginLeft: '8px' })}>Run Query</span>
        </div>
      </DrawerHeader>
      <DrawerBody>
        <div className="flex flex-col gap-[12px]">
          <div className={css({ fontSize: '16px', fontWeight: 700 })}>{query.name}</div>

          <QueryParamForm
            queryParams={queryParams}
            queryPayload={queryPayload}
            onQueryParamChange={onQueryParamChange}
            graphName={graphName}
            paramErrors={paramErrors}
          />
          {query.installed && (
            <div className="flex flex-col gap-[8px]">
              <div className="flex items-center gap-[8px] ">
                <HttpMethodBadge apiType={'POST' as ApiType} />
                <div className={css({ ...theme.typography.Label, color: theme.colors['input.text'], fontWeight: 500 })}>
                  Request URL
                </div>
                <div className="flex flex-1 items-center gap-[8px]">
                  <div className="flex-1">
                    <Input readOnly value={queryURL} />
                  </div>
                  <Button size="compact" kind="text" shape="square" onClick={setUrlCopied}>
                    <CopyIcon />
                  </Button>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <TooltipLabel
                  label="Datebase Token"
                  tooltip="Generate a database token and paste it here so that you can copy the code snippets below to run the query in your own code."
                />

                <Button kind="secondary" size="compact" onClick={() => setIsCreateSecretOpen(true)}>
                  Generate Database Token
                </Button>
              </div>

              <Input
                placeholder="Enter your database secret here"
                value={token}
                onChange={(e) => setToken(e.currentTarget.value)}
              />

              <div>
                <TooltipLabel label="Code Snippets" tooltip="" />
                <SnippetsCopier queryPayload={queryPayload} queryURL={queryURL} secret={token} />
              </div>
            </div>
          )}

          {!query.installed && (
            <StyledToast
              kind={KIND.info}
              closeable={false}
              message="The query is not installed and will be run in interpreted mode. Interpreted queries have limited feature support and may result in slower performance. For optimal execution, please install the query first."
            />
          )}
        </div>
      </DrawerBody>
      <DrawerAction>
        <div className={css({ display: 'flex', gap: '8px', justifyContent: 'flex-end' })}>
          <QueryConfig canDoProfile={query.installed && query.installMode !== 'UDF'} />
          <Button overrides={{ BaseButton: { style: { height: '30px' } } }} size="compact" onClick={onRunClick}>
            Run
          </Button>
        </div>
      </DrawerAction>
      <CreateToken
        isOpen={isCreateSecretOpen}
        onClose={() => setIsCreateSecretOpen(false)}
        workspace={currentWorkspace!}
      />
    </Drawer>
  );
}
